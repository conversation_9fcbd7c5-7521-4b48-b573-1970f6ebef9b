#include "accumulator.h"
Accumulator::Accumulator(const Date& date, double value) : lastDate(date), value(value), sum(0) {}
void Accumulator::change(const Date& date, double value) {
    sum += this->value * (date - lastDate);//用this指针更加规范
    lastDate = date;
    this->value = value;
}
double Accumulator::getSum(const Date& date) {
    return sum + value * (date - lastDate);
}

void Accumulator::reset(const Date& date, double value) {
    sum = 0;
    lastDate = date;
    this->value = value;
}