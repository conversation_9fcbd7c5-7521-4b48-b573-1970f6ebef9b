#include "creditaccount.h"
#include <iostream>
#include <cmath>
using namespace std;
CreditAccount::CreditAccount(const Date& date, const string& id, double credit, double rate, double fee)
    : Account(date, id), acc(date, 0), credit(credit), rate(rate), fee(fee), lastFeeDate(date) {
}
double CreditAccount::getCredit() const {// 获取信用额度
    return credit;
}
double CreditAccount::getAvailableCredit() const {// 与上面的不同，这是获取可用信用额度
     if (getBalance() >= 0) {
        return credit;
    } else {
        return credit + getBalance();
    }
}
double CreditAccount::getRate() const {
    return rate;
}
double CreditAccount::getFee() const {
    return fee;
}
void CreditAccount::deposit(const Date& date, double amount, const string& desc) {
    if (getBalance() < 0) {//只有负余额才需要计息，一开始我没写就错了
        acc.change(date, getBalance());
    }
    record(date, amount, desc);
    if (getBalance() < 0) {//存款后如果仍为负余额，需要更新累加器
        acc.change(date, getBalance());
    } else {
        acc.change(date, 0);//正余额不计息，这里很重要！！！！！
    }
}
void CreditAccount::withdraw(const Date& date, double amount, const string& desc) {
    if (getBalance() - amount >= -credit) {
        if (getBalance() < 0) {//取款前如果为负余额，需要更新累加器
            acc.change(date, getBalance());
        }
        record(date, -amount, desc);
        if (getBalance() < 0) {//取款后如果为负余额，需要更新累加器
            acc.change(date, getBalance());
        } else {
            acc.change(date, 0);//正余额不计息
        }
    } else {
        cout << "Error: not enough credit" << endl;
    }
}
void CreditAccount::settle(const Date& date) {
    if (date.getDay() == 1) { // 每月1日进行结算
        double interest = 0;
        if (getBalance() < 0) { // 只有欠款才计算利息
            interest = acc.getSum(date) * rate;
            interest = floor(interest * 100 + 0.5) / 100; // 四舍五入到分
            if (interest != 0) {
                // 在record之前先记录当前的total
                double oldTotal = total;
                
                // 记录利息
                record(date, interest, "interest");
                
                // 确保total正确更新
                if (abs(total - (oldTotal + interest)) > 0.01) {
                    total = oldTotal;
                    total -= getBalance() - interest;
                    total += getBalance();
                }
            }
        }
        
        // 每年1月收取年费
        if (date.getMonth() == 1) {
            withdraw(date, fee, "annual fee");
        }
        
        // 重置累加器，如果有欠款则保留当前余额，否则设为0
        acc.reset(date, getBalance() < 0 ? getBalance() : 0);
    }
}
void CreditAccount::show() const {
    Account::show();
    cout << "\tAvailable credit:" << getAvailableCredit();
}


































