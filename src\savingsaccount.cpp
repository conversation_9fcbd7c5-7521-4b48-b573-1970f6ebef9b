#include "savingsaccount.h"
#include <iostream>
#include <cmath>
using namespace std;
extern bool isLeapYear(int year);// 为了不再重复定义isLeapYear函数，使用date.cpp中已定义的函数，声明为外部函数
SavingsAccount::SavingsAccount(const Date& date, const string& id, double rate)
    : Account(date, id), acc(date, 0), rate(rate) {
}
void SavingsAccount::deposit(const Date& date, double amount, const string& desc) {
    acc.change(date, getBalance() + amount);
    record(date, amount, desc);
}
void SavingsAccount::withdraw(const Date& date, double amount, const string& desc) {
    if (amount <= getBalance()) {
        acc.change(date, getBalance() - amount);
        record(date, -amount, desc);
    } else {
        cout << "Error: not enough money" << endl;
    }
}
void SavingsAccount::settle(const Date& date) {
      if (date.getMonth() == 1 && date.getDay() == 1) { // 只在1月1日进行结算
        int days = 365;
        if (isLeapYear(date.getYear() - 1)) {
            days = 366;
        }
        double interest = acc.getSum(date) * rate / days; // 计算年利息 = 日均余额 * 年利率
        interest = floor(interest * 100 + 0.5) / 100; // 利息四舍五入到分
        if (interest != 0) {
            record(date, interest, "interest");
        }
        acc.reset(date, getBalance()); // 重置累加器，但保留当前余额
    }
}
void SavingsAccount::show() const {
    Account::show();
}
