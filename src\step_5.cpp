//step5.cpp

#include "account.h"
#include "savingsaccount.h"
#include "creditaccount.h"

#include <iostream>
#include <vector>
#include <algorithm>
#include <fstream>
#include <sstream>
#include <string>
using namespace std;



struct deleter {

template <class T> void operator () (T* p) { delete p; }

};



int main() {

Date date(2008, 11, 1);//起始日期
vector<Account *> accounts;//创建账户数组，元素个数为0
    ifstream commandFile("src/commands.txt");
    if (commandFile) {
        string line;
        while (getline(commandFile, line)) {
            if (line.empty()) continue;

            istringstream iss(line);
            char cmd;
            iss >> cmd;

            switch (cmd) {
                case 'a': {
                    char type;
                    string id;
                    iss >> type >> id;

                    if (type == 's') {
                        double rate;
                        iss >> rate;
                        accounts.push_back(new SavingsAccount(date, id, rate));
                    } else {
                        double credit, rate, fee;
                        iss >> credit >> rate >> fee;
                        accounts.push_back(new CreditAccount(date, id, credit, rate, fee));
                    }
                    break;
                }
                case 'd': {
                    int index;
                    double amount;
                    string desc;
                    iss >> index >> amount;
                    getline(iss, desc);
                    accounts[index]->deposit(date, amount, desc);
                    break;
                }
                case 'w': {
                    int index;
                    double amount;
                    string desc;
                    iss >> index >> amount;
                    getline(iss, desc);
                    accounts[index]->withdraw(date, amount, desc);
                    break;
                }
                case 'c': {
                    int day;
                    iss >> day;
                    if (day >= date.getDay() && day <= date.getMaxDay())
                        date = Date(date.getYear(), date.getMonth(), day);
                    break;
                }
                case 'n': {
                    if (date.getMonth() == 12)
                        date = Date(date.getYear() + 1, 1, 1);
                    else
                        date = Date(date.getYear(), date.getMonth() + 1, 1);

                    for (vector<Account*>::iterator iter = accounts.begin(); iter != accounts.end(); ++iter)
                        (*iter)->settle(date);
                    break;
                }
            }
        }
        commandFile.close();
    } else {
        cout << "无法打开命令文件: src/commands.txt" << endl;
    }

    // 打开文件以追加新命令
    ofstream outCommandFile("src/commands.txt", ios::app);
    cout << "(a)add account (d)deposit (w)withdraw (s)show (c)change day (n)next month (q)query (e)exit" << endl;

    char cmd;
    do {
        // 显示日期和总金额
        date.show();
        cout << "\tTotal: " << Account::getTotal() << "\tcommand> ";

        char type;
        int index, day;
        double amount, credit, rate, fee;
        string id, desc;
        Account* account;
        Date date1, date2;

        cin >> cmd;

        // 记录命令到文件
        stringstream commandStream;
        commandStream << cmd;

        switch (cmd) {
            case 'a': {
                cin >> type >> id;
                commandStream << " " << type << " " << id;

                if (type == 's') {
                    cin >> rate;
                    commandStream << " " << rate;
                    account = new SavingsAccount(date, id, rate);
                } else {
                    cin >> credit >> rate >> fee;
                    commandStream << " " << credit << " " << rate << " " << fee;
                    account = new CreditAccount(date, id, credit, rate, fee);
                }
                accounts.push_back(account);
                break;
            }
            case 'd': {
                cin >> index >> amount;
                getline(cin, desc);
                commandStream << " " << index << " " << amount << desc;
                accounts[index]->deposit(date, amount, desc);
                break;
            }
            case 'w': {
                cin >> index >> amount;
                getline(cin, desc);
                commandStream << " " << index << " " << amount << desc;
                accounts[index]->withdraw(date, amount, desc);
                break;
            }
            case 's': {
                for (size_t i = 0; i < accounts.size(); i++) {
                    cout << "[" << i << "] ";
                    accounts[i]->show();
                    cout << endl;
                }
                continue; // 不记录s命令
            }
            case 'c': {
                cin >> day;
                commandStream << " " << day;
                if (day < date.getDay())
                    cout << "You cannot specify a previous day";
                else if (day > date.getMaxDay())
                    cout << "Invalid day";
                else
                    date = Date(date.getYear(), date.getMonth(), day);
                break;
            }
            case 'n': {
                if (date.getMonth() == 12)
                    date = Date(date.getYear() + 1, 1, 1);
                else
                    date = Date(date.getYear(), date.getMonth() + 1, 1);

                for (vector<Account*>::iterator iter = accounts.begin(); iter != accounts.end(); ++iter)
                    (*iter)->settle(date);
                break;
            }
            case 'q': {
                date1 = Date::read();
                date2 = Date::read();
                Account::query(date1, date2);
                continue; // 不记录q命令
            }
            case 'e':
                continue; // 不记录e命令
        }

        // 将命令写入文件（除了s、q和e命令）
        if (cmd != 's' && cmd != 'q' && cmd != 'e') {
            outCommandFile << commandStream.str() << endl;
            outCommandFile.flush(); // 确保立即写入文件
        }

    } while (cmd != 'e');

    outCommandFile.close();

    for_each(accounts.begin(), accounts.end(), deleter());
    return 0;
}
